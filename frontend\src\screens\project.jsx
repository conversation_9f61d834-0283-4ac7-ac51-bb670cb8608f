import React, { useState } from 'react';
import { useLocation } from 'react-router-dom';

const Project = () => {
  const location = useLocation();
  const [showUserPanel, setShowUserPanel] = useState(false);

  const user = location.state || { username: 'Unknown', email: '<EMAIL>' };

  const messages = [
    { email: '<EMAIL>', text: 'Hello there! 🌞', self: false },
    { email: user.email, text: 'Hello', self: true },
    { email: '<EMAIL>', text: 'How are you?', self: false },
    { email: user.email, text: 'Doing great, thanks!', self: true },
  ];

  return (
    <div className="flex h-screen font-sans relative">
      {/* Left Sidebar */}
      <div className="w-1/3 bg-[#e0f2f1] flex flex-col justify-between border-r border-[#b2dfdb]">
        {/* Top Profile */}
        <div className="bg-[#b2ebf2] p-4 flex justify-between ">
          <button>
           <i className="ri-user-add-line flex gap-2 items-center text-xl"><span className='text-md'>Add Collabarator</span></i>
          </button>
          <button onClick={() => setShowUserPanel(true)}>
            <i className="ri-group-line text-2xl"></i>
          </button>
        </div>

        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto px-4 py-2 space-y-4 bg-[#e0f2f1]">
          {messages.map((msg, idx) => (
            <div
              key={idx}
              className={`flex flex-col max-w-[80%] ${
                msg.self ? 'ml-auto text-right items-end' : 'items-start'
              }`}
            >
              <span className="text-xs text-gray-600 mb-1">{msg.email}</span>
              <div
                className={`p-2 rounded-lg text-sm shadow ${
                  msg.self
                    ? 'bg-[#d1c4e9] text-gray-800'
                    : 'bg-[#c8e6c9] text-gray-800'
                }`}
              >
                {msg.text}
              </div>
            </div>
          ))}
        </div>

        {/* Bottom Input */}
        <div className="bg-white p-4 flex items-center border-t border-[#b2dfdb]">
          <input
            type="text"
            placeholder="Type your message..."
            className="flex-1 p-2 rounded-md border border-gray-300 focus:ring-2 focus:ring-teal-400 focus:outline-none"
          />
          <button className="ml-3 px-4 py-2 bg-teal-400 text-white rounded-md shadow hover:bg-teal-500 transition">
            Send
          </button>
        </div>
      </div>

      {/* Right Side - Empty */}
      <div className="flex-1 bg-[#f1f8f9]"></div>

      {/* Side User Panel */}
      {showUserPanel && (
        <div className="absolute top-0 right-0 h-full w-64 bg-white shadow-lg z-50 transition-transform duration-300">
          <div className="flex justify-between items-center p-4 border-b">
            <h2 className="text-lg font-semibold">User Info</h2>
            <button
              onClick={() => setShowUserPanel(false)}
              className="text-xl text-gray-500 hover:text-red-500"
            >
              &times;
            </button>
          </div>
          <div className="p-4 space-y-2">
            <p className="text-gray-700">
              <span className="font-semibold">Username:</span> {user.username}
            </p>
            <p className="text-gray-700">
              <span className="font-semibold">Email:</span> {user.email}
            </p>
          </div>
        </div>
      )}
    </div>
  );
};

export default Project;
