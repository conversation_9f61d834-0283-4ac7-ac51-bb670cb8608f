import React from 'react'
import { Routes, Route, BrowserRouter } from 'react-router-dom'
import Login from '../screens/Login.jsx'
import Register from '../screens/Register.jsx'
import Home from '../screens/Home.jsx'
import Project from '../screens/project.jsx'

const AppRoutes = () => {
  return (
    <BrowserRouter>
      <Routes>
        <Route path='/' element={<Home />} />
        <Route path='/login' element={<Login />} />
        <Route path='/register' element={<Register />} />
         <Route path='/projects' element={<Project />} />  
      </Routes>
    </BrowserRouter>
  )
}

export default AppRoutes
